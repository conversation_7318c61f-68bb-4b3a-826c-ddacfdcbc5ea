<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RenameToCompany extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $oldName="restorants";
        $newName="companies";

        // Only rename if the old table exists and the new table doesn't exist
        if (Schema::hasTable($oldName) && !Schema::hasTable($newName)) {
            Schema::rename($oldName, $newName);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $newName="restorants";
        $oldName="companies";
        
        //Rename the table
        Schema::rename($oldName, $newName);
    }
}
